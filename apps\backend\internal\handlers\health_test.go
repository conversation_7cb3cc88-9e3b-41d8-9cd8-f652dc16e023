package handlers

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestHealthCheck(t *testing.T) {
	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Create a new router
	router := gin.New()
	router.GET("/api/v1/health", HealthCheck)

	// Create a test request
	req, err := http.NewRequest("GET", "/api/v1/health", nil)
	assert.NoError(t, err)

	// Create a response recorder
	w := httptest.NewRecorder()

	// Perform the request
	router.ServeHTTP(w, req)

	// Check the status code
	assert.Equal(t, http.StatusOK, w.Code)

	// Parse the response
	var response HealthCheckResponse
	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	// Verify the response structure
	assert.Equal(t, "healthy", response.Status)
	assert.Equal(t, "1.0.0", response.Version)
	assert.NotEmpty(t, response.Timestamp)

	// Verify timestamp is valid
	_, err = time.Parse(time.RFC3339, response.Timestamp)
	assert.NoError(t, err)

	// Verify services are present
	assert.NotNil(t, response.Services)
	assert.Contains(t, response.Services, "database")
	assert.Contains(t, response.Services, "redis")
	assert.Contains(t, response.Services, "kafka")

	// Verify Content-Type header
	assert.Equal(t, "application/json; charset=utf-8", w.Header().Get("Content-Type"))
}

func TestHealthCheckResponseStructure(t *testing.T) {
	// Test that the response structure matches expected format
	response := HealthCheckResponse{
		Status:    "healthy",
		Timestamp: time.Now().UTC().Format(time.RFC3339),
		Version:   "1.0.0",
		Services: map[string]interface{}{
			"database": "connected",
			"redis":    "connected",
			"kafka":    "connected",
		},
	}

	// Verify all fields are properly set
	assert.Equal(t, "healthy", response.Status)
	assert.Equal(t, "1.0.0", response.Version)
	assert.NotEmpty(t, response.Timestamp)
	assert.Len(t, response.Services, 3)
}
