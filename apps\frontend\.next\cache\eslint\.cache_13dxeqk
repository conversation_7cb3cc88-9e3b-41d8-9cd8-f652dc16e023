[{"C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\app\\health\\page.tsx": "1", "C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\app\\layout.tsx": "2", "C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\app\\page.tsx": "3", "C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\components\\layout.tsx": "4", "C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\lib\\utils.ts": "5", "C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\stores\\auth-store.ts": "6"}, {"size": 3910, "mtime": 1754314487080, "results": "7", "hashOfConfig": "8"}, {"size": 689, "mtime": 1754313585056, "results": "9", "hashOfConfig": "8"}, {"size": 1268, "mtime": 1754314278321, "results": "10", "hashOfConfig": "8"}, {"size": 765, "mtime": 1754314239945, "results": "11", "hashOfConfig": "8"}, {"size": 169, "mtime": 1754314159964, "results": "12", "hashOfConfig": "8"}, {"size": 737, "mtime": 1754314473145, "results": "13", "hashOfConfig": "8"}, {"filePath": "14", "messages": "15", "suppressedMessages": "16", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1okb4zw", {"filePath": "17", "messages": "18", "suppressedMessages": "19", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\app\\health\\page.tsx", ["32"], [], "C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\components\\layout.tsx", [], [], "C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\stores\\auth-store.ts", [], [], {"ruleId": "33", "severity": 1, "message": "34", "line": 31, "column": 16, "nodeType": null, "messageId": "35", "endLine": 31, "endColumn": 19}, "@typescript-eslint/no-unused-vars", "'err' is defined but never used.", "unusedVar"]