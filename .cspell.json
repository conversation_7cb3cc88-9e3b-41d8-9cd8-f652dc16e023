{"version": "0.2", "language": "en", "words": ["OmniCore", "Monore<PERSON>", "Turborepo", "shadcn", "Zustand", "PostgreSQL", "Redis", "Kafka", "Dockerfile", "kubernetes", "kubectl", "namespace", "configmap", "secretmap", "healthcheck", "middleware", "CORS", "JWT", "OAuth", "<PERSON><PERSON><PERSON>", "WMS", "POS", "SKU", "COGS", "API", "REST", "GraphQL", "JSON", "YAML", "TOML", "env", "dotenv", "eslint", "prettier", "<PERSON><PERSON><PERSON>", "codecov", "trivy", "govulncheck", "cspell"], "ignorePaths": ["node_modules/**", ".git/**", "dist/**", "build/**", ".next/**", "coverage/**", "*.log"]}