1:"$Sreact.fragment"
2:I[7132,[],""]
3:I[5082,[],""]
4:I[700,[],"OutletBoundary"]
6:I[7748,[],"AsyncMetadataOutlet"]
8:I[700,[],"ViewportBoundary"]
a:I[700,[],"MetadataBoundary"]
b:"$Sreact.suspense"
d:I[1256,[],""]
:HL["/_next/static/css/b4bc6f2cc5a95344.css","style"]
0:{"P":null,"b":"x9FuoEAkEEOzL_DVJn6tq","p":"","c":["",""],"i":false,"f":[[["",{"children":["__PAGE__",{}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/b4bc6f2cc5a95344.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","children":["$","body",null,{"className":"__variable_5cfdac __variable_9a8899 antialiased","children":["$","$L2",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L3",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","div",null,{"className":"min-h-screen bg-background","children":[["$","header",null,{"className":"border-b","children":["$","div",null,{"className":"container mx-auto px-4 py-4","children":["$","h1",null,{"className":"text-2xl font-bold text-foreground","children":"OmniCore Business Management"}]}]}],["$","main",null,{"className":"container mx-auto px-4 py-8","children":["$","div",null,{"className":"text-center","children":[["$","h1",null,{"className":"text-4xl font-bold mb-6","children":"Welcome to OmniCore"}],["$","p",null,{"className":"text-xl text-muted-foreground mb-8","children":"Integrated Business Management Ecosystem"}],["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto","children":[["$","div",null,{"className":"p-6 border rounded-lg","children":[["$","h3",null,{"className":"text-lg font-semibold mb-2","children":"WMS"}],["$","p",null,{"className":"text-muted-foreground","children":"Warehouse Management System for inventory control"}]]}],["$","div",null,{"className":"p-6 border rounded-lg","children":[["$","h3",null,{"className":"text-lg font-semibold mb-2","children":"POS"}],["$","p",null,{"className":"text-muted-foreground","children":"Point of Sale system for retail operations"}]]}],["$","div",null,{"className":"p-6 border rounded-lg","children":[["$","h3",null,{"className":"text-lg font-semibold mb-2","children":"Accounting"}],["$","p",null,{"className":"text-muted-foreground","children":"Automated accounting and financial reporting"}]]}]]}]]}]}],["$","footer",null,{"className":"border-t mt-auto","children":["$","div",null,{"className":"container mx-auto px-4 py-4 text-center text-muted-foreground","children":["$","p",null,{"children":"© 2025 OmniCore. All rights reserved."}]}]}]]}],null,["$","$L4",null,{"children":["$L5",["$","$L6",null,{"promise":"$@7"}]]}]]}],{},null,false]},null,false],["$","$1","h",{"children":[null,[["$","$L8",null,{"children":"$L9"}],null],["$","$La",null,{"children":["$","div",null,{"hidden":true,"children":["$","$b",null,{"fallback":null,"children":"$Lc"}]}]}]]}],false]],"m":"$undefined","G":["$d",[]],"s":false,"S":true}
9:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
5:null
e:I[4780,[],"IconMark"]
7:{"metadata":[["$","title","0",{"children":"Create Next App"}],["$","meta","1",{"name":"description","content":"Generated by create next app"}],["$","link","2",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}],["$","$Le","3",{}]],"error":null,"digest":"$undefined"}
c:"$7:metadata"
