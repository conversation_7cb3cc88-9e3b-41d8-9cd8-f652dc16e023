# OmniCore Deployment Guide

## Overview

This document describes the deployment process for the OmniCore application using GitHub Actions CI/CD pipelines and Kubernetes.

## Architecture

- **Frontend**: Next.js application deployed as Docker container
- **Backend**: Go application deployed as Docker container  
- **Database**: PostgreSQL (managed service recommended)
- **Cache**: Redis (managed service recommended)
- **Message Queue**: Apache Kafka (managed service recommended)

## Environments

### Development
- **URL**: https://dev.omnicore.example.com
- **Namespace**: `omnicore-dev`
- **Auto-deployment**: On push to `main` branch
- **Resources**: 2 replicas each for frontend/backend

### Staging
- **URL**: https://staging.omnicore.example.com
- **Namespace**: `omnicore-staging`
- **Deployment**: Manual trigger with approval
- **Resources**: 2 replicas each for frontend/backend

### Production
- **URL**: https://omnicore.example.com
- **Namespace**: `omnicore-prod`
- **Deployment**: Manual trigger with approval
- **Resources**: 3+ replicas each for frontend/backend

## CI/CD Pipeline

### Continuous Integration (CI)
Triggered on push/PR to `main` or `develop` branches:

1. **Frontend CI**
   - Type checking with TypeScript
   - Linting with ESLint
   - Building Next.js application
   - Running tests (Jest/React Testing Library)

2. **Backend CI**
   - Go vet and formatting checks
   - Running tests with race detection
   - Building Go binary
   - Code coverage reporting

3. **Security & Quality**
   - Vulnerability scanning with Trivy
   - Dependency auditing
   - Code quality checks

### Continuous Deployment (CD)
Triggered on push to `main` or manual workflow dispatch:

1. **Build Images**
   - Build Docker images for frontend and backend
   - Push to GitHub Container Registry (ghcr.io)
   - Tag with branch name and commit SHA

2. **Deploy to Development**
   - Automatic deployment on `main` branch
   - Update Kubernetes manifests with new image tags
   - Deploy to development cluster
   - Run deployment verification tests

3. **Deploy to Staging/Production**
   - Manual trigger with environment selection
   - Requires approval for staging/production
   - Comprehensive smoke tests for production

## Prerequisites

### GitHub Secrets
Configure the following secrets in your GitHub repository:

```bash
# Kubernetes configurations (base64 encoded)
KUBE_CONFIG_DEV      # Development cluster kubeconfig
KUBE_CONFIG_STAGING  # Staging cluster kubeconfig  
KUBE_CONFIG_PROD     # Production cluster kubeconfig
```

### Kubernetes Cluster Setup
1. Create namespaces for each environment
2. Configure RBAC permissions
3. Set up ingress controllers
4. Configure persistent volumes for databases

## Manual Deployment

### Prerequisites
- kubectl configured for target cluster
- Docker images built and pushed to registry

### Steps
1. Update image tags in deployment manifests
2. Apply Kubernetes manifests:
   ```bash
   kubectl apply -f infrastructure/k8s/development/
   ```
3. Verify deployment:
   ```bash
   kubectl rollout status deployment/frontend-deployment -n omnicore-dev
   kubectl rollout status deployment/backend-deployment -n omnicore-dev
   ```

## Monitoring and Troubleshooting

### Health Checks
- **Backend**: `GET /api/v1/health`
- **Frontend**: `GET /health`

### Logs
```bash
# View application logs
kubectl logs -f deployment/backend-deployment -n omnicore-dev
kubectl logs -f deployment/frontend-deployment -n omnicore-dev

# View events
kubectl get events -n omnicore-dev --sort-by='.lastTimestamp'
```

### Common Issues
1. **Image pull errors**: Check registry permissions and image tags
2. **Database connection**: Verify service names and secrets
3. **Resource limits**: Check pod resource requests/limits

## Rollback Procedure

### Automatic Rollback
Kubernetes will automatically rollback if:
- Readiness probes fail
- Deployment doesn't complete within timeout

### Manual Rollback
```bash
# View rollout history
kubectl rollout history deployment/backend-deployment -n omnicore-dev

# Rollback to previous version
kubectl rollout undo deployment/backend-deployment -n omnicore-dev

# Rollback to specific revision
kubectl rollout undo deployment/backend-deployment --to-revision=2 -n omnicore-dev
```

## Security Considerations

1. **Secrets Management**: Use Kubernetes secrets or external secret managers
2. **Network Policies**: Implement network segmentation between namespaces
3. **RBAC**: Configure least-privilege access for service accounts
4. **Image Scanning**: All images are scanned for vulnerabilities in CI
5. **TLS**: Use TLS termination at ingress level

## Performance Optimization

1. **Resource Limits**: Set appropriate CPU/memory limits
2. **Horizontal Pod Autoscaling**: Configure HPA based on CPU/memory usage
3. **Caching**: Implement Redis caching for frequently accessed data
4. **CDN**: Use CDN for static assets in production

## Backup and Recovery

1. **Database**: Regular automated backups of PostgreSQL
2. **Configuration**: Store all configuration in version control
3. **Disaster Recovery**: Document recovery procedures for each environment
