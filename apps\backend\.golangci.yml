run:
  timeout: 5m
  issues-exit-code: 1
  tests: true

output:
  format: colored-line-number
  print-issued-lines: true
  print-linter-name: true

linters-settings:
  errcheck:
    check-type-assertions: true
    check-blank: true
  
  govet:
    check-shadowing: true
  
  gocyclo:
    min-complexity: 15
  
  maligned:
    suggest-new: true
  
  dupl:
    threshold: 100
  
  goconst:
    min-len: 3
    min-occurrences: 3
  
  misspell:
    locale: US
  
  lll:
    line-length: 120
  
  goimports:
    local-prefixes: omnicore/backend
  
  gocritic:
    enabled-tags:
      - performance
      - style
      - experimental
    disabled-checks:
      - wrapperFunc

linters:
  enable:
    - errcheck
    - gosimple
    - govet
    - ineffassign
    - staticcheck
    - typecheck
    - unused
    - varcheck
    - structcheck
    - deadcode
    - gocyclo
    - gofmt
    - goimports
    - misspell
    - lll
    - goconst
    - gocritic
    - gci
    - godot
    - godox
    - gofumpt
    - gosec
    - nolintlint
    - revive
    - unconvert
    - unparam
    - whitespace

issues:
  exclude-rules:
    - path: _test\.go
      linters:
        - gocyclo
        - errcheck
        - dupl
        - gosec
    - path: cmd/
      linters:
        - gocyclo
    - linters:
        - lll
      source: "^//go:generate "

  max-issues-per-linter: 0
  max-same-issues: 0
