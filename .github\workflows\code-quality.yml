name: Code Quality

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  # Frontend code quality
  frontend-quality:
    name: Frontend Code Quality
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./apps/frontend

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: '**/package-lock.json'

      - name: Install root dependencies
        run: npm ci
        working-directory: ./

      - name: Install frontend dependencies
        run: npm ci

      - name: ESLint
        run: npm run lint

      - name: Prettier check
        run: npx prettier --check "src/**/*.{ts,tsx,js,jsx,json,css,md}"

      - name: Type check
        run: npm run type-check

  # Backend code quality
  backend-quality:
    name: Backend Code Quality
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./apps/backend

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Go
        uses: actions/setup-go@v5
        with:
          go-version: '1.22'

      - name: golangci-lint
        uses: golangci/golangci-lint-action@v4
        with:
          version: latest
          working-directory: apps/backend

      - name: Go fmt check
        run: |
          if [ "$(gofmt -s -l . | wc -l)" -gt 0 ]; then
            echo "The following files are not formatted:"
            gofmt -s -l .
            exit 1
          fi

      - name: Go vet
        run: go vet ./...

      - name: Go mod tidy check
        run: |
          go mod tidy
          if [ -n "$(git status --porcelain)" ]; then
            echo "go mod tidy resulted in changes"
            git status --porcelain
            exit 1
          fi

  # Documentation check
  docs-quality:
    name: Documentation Quality
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Check for README
        run: |
          if [ ! -f README.md ]; then
            echo "README.md is missing"
            exit 1
          fi

      - name: Check markdown links
        uses: gaurav-nelson/github-action-markdown-link-check@v1
        with:
          use-quiet-mode: 'yes'
          use-verbose-mode: 'yes'
          config-file: '.github/markdown-link-check-config.json'

      - name: Spell check
        uses: streetsidesoftware/cspell-action@v6
        with:
          files: |
            **/*.md
            **/*.txt
          config: .cspell.json
