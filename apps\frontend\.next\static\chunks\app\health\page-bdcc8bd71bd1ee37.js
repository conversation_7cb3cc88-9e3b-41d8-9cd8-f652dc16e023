(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[653],{8144:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d});var a=t(4568),r=t(7620);function n(e){let{children:s}=e;return(0,a.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,a.jsx)("header",{className:"border-b",children:(0,a.jsx)("div",{className:"container mx-auto px-4 py-4",children:(0,a.jsx)("h1",{className:"text-2xl font-bold text-foreground",children:"OmniCore Business Management"})})}),(0,a.jsx)("main",{className:"container mx-auto px-4 py-8",children:s}),(0,a.jsx)("footer",{className:"border-t mt-auto",children:(0,a.jsx)("div",{className:"container mx-auto px-4 py-4 text-center text-muted-foreground",children:(0,a.jsx)("p",{children:"\xa9 2025 OmniCore. All rights reserved."})})})]})}function d(){let[e,s]=(0,r.useState)(null),[t,d]=(0,r.useState)(!0),[c,l]=(0,r.useState)(null);return(0,r.useEffect)(()=>{(async()=>{try{let e={status:"healthy",timestamp:new Date().toISOString(),version:"1.0.0",services:{database:"connected",redis:"connected",kafka:"connected"}};await new Promise(e=>setTimeout(e,1e3)),s(e)}catch(e){l("Failed to fetch health status")}finally{d(!1)}})()},[]),(0,a.jsx)(n,{children:(0,a.jsxs)("div",{className:"max-w-2xl mx-auto",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold mb-6",children:"System Health Check"}),t&&(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"}),(0,a.jsx)("p",{className:"mt-2 text-muted-foreground",children:"Checking system health..."})]}),c&&(0,a.jsx)("div",{className:"bg-destructive/10 border border-destructive/20 rounded-lg p-4",children:(0,a.jsx)("p",{className:"text-destructive",children:c})}),e&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"p-4 rounded-lg border ".concat("healthy"===e.status?"bg-green-50 border-green-200":"bg-red-50 border-red-200"),children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full ".concat("healthy"===e.status?"bg-green-500":"bg-red-500")}),(0,a.jsxs)("span",{className:"font-semibold",children:["Status: ",e.status.toUpperCase()]})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,a.jsx)("h3",{className:"font-semibold mb-2",children:"System Info"}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Version: ",e.version]}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Timestamp: ",new Date(e.timestamp).toLocaleString()]})]}),e.services&&(0,a.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,a.jsx)("h3",{className:"font-semibold mb-2",children:"Services"}),(0,a.jsx)("div",{className:"space-y-1",children:Object.entries(e.services).map(e=>{let[s,t]=e;return(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsxs)("span",{className:"capitalize",children:[s,":"]}),(0,a.jsx)("span",{className:"connected"===t?"text-green-600":"text-red-600",children:String(t)})]},s)})})]})]})]})]})})}},9306:(e,s,t)=>{Promise.resolve().then(t.bind(t,8144))}},e=>{e.O(0,[587,902,358],()=>e(e.s=9306)),_N_E=e.O()}]);