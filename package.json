{"name": "omnicore-monorepo", "version": "1.0.0", "description": "Integrated Business Management Ecosystem - Monorepo", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "test": "turbo run test", "type-check": "turbo run type-check", "clean": "turbo run clean", "docker:dev": "docker-compose up -d postgres redis zookeeper kafka", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:ps": "docker-compose ps", "setup": "npm install && npm run docker:dev", "setup:full": "powershell -ExecutionPolicy Bypass -File scripts/dev-setup.ps1", "health": "curl http://localhost:8080/api/v1/health"}, "devDependencies": {"turbo": "^1.13.0", "@types/node": "^20.0.0", "typescript": "^5.4.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "packageManager": "npm@10.0.0"}