# OmniCore Development Setup Script for Windows
Write-Host "🚀 Setting up OmniCore development environment..." -ForegroundColor Green

# Check if Docker is running
try {
    docker info | Out-Null
} catch {
    Write-Host "❌ Docker is not running. Please start Docker and try again." -ForegroundColor Red
    exit 1
}

# Create .env file if it doesn't exist
if (-not (Test-Path ".env")) {
    Write-Host "📝 Creating .env file from .env.example..." -ForegroundColor Yellow
    Copy-Item ".env.example" ".env"
    Write-Host "✅ .env file created. Please review and update the values as needed." -ForegroundColor Green
}

# Install root dependencies
Write-Host "📦 Installing root dependencies..." -ForegroundColor Yellow
npm install

# Install frontend dependencies
Write-Host "📦 Installing frontend dependencies..." -ForegroundColor Yellow
Set-Location "apps/frontend"
npm install
Set-Location "../.."

# Build and start services
Write-Host "🐳 Starting Docker services..." -ForegroundColor Yellow
docker-compose up -d postgres redis zookeeper kafka

# Wait for services to be healthy
Write-Host "⏳ Waiting for services to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# Check service health
Write-Host "🔍 Checking service health..." -ForegroundColor Yellow
docker-compose ps

Write-Host "✅ Development environment setup complete!" -ForegroundColor Green
Write-Host ""
Write-Host "🎯 Next steps:" -ForegroundColor Cyan
Write-Host "  1. Review .env file and update values as needed"
Write-Host "  2. Run 'npm run dev' to start the development servers"
Write-Host "  3. Visit http://localhost:3000 for frontend"
Write-Host "  4. Visit http://localhost:8080/api/v1/health for backend health check"
Write-Host ""
Write-Host "🛠️  Useful commands:" -ForegroundColor Cyan
Write-Host "  - npm run dev          # Start all development servers"
Write-Host "  - npm run docker:dev   # Start Docker services only"
Write-Host "  - npm run docker:down  # Stop Docker services"
Write-Host "  - npm run build        # Build all applications"
Write-Host "  - npm run test         # Run all tests"
