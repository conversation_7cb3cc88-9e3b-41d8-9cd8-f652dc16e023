globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"700":{"*":{"id":"9082","name":"*","chunks":[],"async":false}},"1256":{"*":{"id":"2526","name":"*","chunks":[],"async":false}},"3283":{"*":{"id":"3737","name":"*","chunks":[],"async":false}},"4712":{"*":{"id":"1904","name":"*","chunks":[],"async":false}},"4780":{"*":{"id":"3220","name":"*","chunks":[],"async":false}},"5082":{"*":{"id":"5812","name":"*","chunks":[],"async":false}},"7132":{"*":{"id":"5856","name":"*","chunks":[],"async":false}},"7748":{"*":{"id":"5492","name":"*","chunks":[],"async":false}},"8144":{"*":{"id":"3789","name":"*","chunks":[],"async":false}},"9065":{"*":{"id":"385","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Project\\OmniCore\\node_modules\\next\\dist\\client\\components\\builtin\\global-error.js":{"id":1256,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\node_modules\\next\\dist\\esm\\client\\components\\builtin\\global-error.js":{"id":1256,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":9065,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":9065,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":3283,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":3283,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":4712,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":4712,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":7132,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":7132,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":7748,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":7748,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":700,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":700,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":5082,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":5082,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\node_modules\\next\\dist\\lib\\metadata\\generate\\icon-mark.js":{"id":4780,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\node_modules\\next\\dist\\esm\\lib\\metadata\\generate\\icon-mark.js":{"id":4780,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}":{"id":4703,"name":"*","chunks":["177","static/chunks/app/layout-299920cea797dbe2.js"],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}":{"id":7573,"name":"*","chunks":["177","static/chunks/app/layout-299920cea797dbe2.js"],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\app\\globals.css":{"id":3139,"name":"*","chunks":["177","static/chunks/app/layout-299920cea797dbe2.js"],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\app\\health\\page.tsx":{"id":8144,"name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\":[],"C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\app\\layout":[{"inlined":false,"path":"static/css/b4bc6f2cc5a95344.css"}],"C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\app\\page":[]},"rscModuleMapping":{"700":{"*":{"id":"968","name":"*","chunks":[],"async":false}},"1256":{"*":{"id":"3824","name":"*","chunks":[],"async":false}},"3139":{"*":{"id":"4276","name":"*","chunks":[],"async":false}},"3283":{"*":{"id":"4439","name":"*","chunks":[],"async":false}},"4712":{"*":{"id":"4730","name":"*","chunks":[],"async":false}},"4780":{"*":{"id":"282","name":"*","chunks":[],"async":false}},"5082":{"*":{"id":"8298","name":"*","chunks":[],"async":false}},"7132":{"*":{"id":"9774","name":"*","chunks":[],"async":false}},"7748":{"*":{"id":"3170","name":"*","chunks":[],"async":false}},"8144":{"*":{"id":"5855","name":"*","chunks":[],"async":false}},"9065":{"*":{"id":"9355","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{"700":{"*":{"id":"9082","name":"*","chunks":[],"async":false}},"1256":{"*":{"id":"2526","name":"*","chunks":[],"async":false}},"3283":{"*":{"id":"3737","name":"*","chunks":[],"async":false}},"4712":{"*":{"id":"1904","name":"*","chunks":[],"async":false}},"4780":{"*":{"id":"3220","name":"*","chunks":[],"async":false}},"5082":{"*":{"id":"5812","name":"*","chunks":[],"async":false}},"7132":{"*":{"id":"5856","name":"*","chunks":[],"async":false}},"7748":{"*":{"id":"5492","name":"*","chunks":[],"async":false}},"9065":{"*":{"id":"385","name":"*","chunks":[],"async":false}}}}