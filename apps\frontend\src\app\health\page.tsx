'use client';

import { useEffect, useState } from 'react';
import { Layout } from '@/components/layout';
import type { HealthCheckResponse } from '@omnicore/types';

export default function HealthPage() {
  const [healthData, setHealthData] = useState<HealthCheckResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const checkHealth = async () => {
      try {
        const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080';
        const response = await fetch(`${apiUrl}/api/v1/health`);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const healthData: HealthCheckResponse = await response.json();
        setHealthData(healthData);
      } catch (err) {
        console.error('Health check failed:', err);
        setError('Failed to fetch health status from backend');

        // Fallback to mock data for development
        const mockHealthData: HealthCheckResponse = {
          status: 'unhealthy',
          timestamp: new Date().toISOString(),
          version: '1.0.0',
          services: {
            database: 'disconnected',
            redis: 'disconnected',
            kafka: 'disconnected',
          },
        };
        setHealthData(mockHealthData);
      } finally {
        setLoading(false);
      }
    };

    checkHealth();
  }, []);

  return (
    <Layout>
      <div className="max-w-2xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">System Health Check</h1>
        
        {loading && (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Checking system health...</p>
          </div>
        )}

        {error && (
          <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-4">
            <p className="text-destructive">{error}</p>
          </div>
        )}

        {healthData && (
          <div className="space-y-4">
            <div className={`p-4 rounded-lg border ${
              healthData.status === 'healthy' 
                ? 'bg-green-50 border-green-200' 
                : 'bg-red-50 border-red-200'
            }`}>
              <div className="flex items-center gap-2">
                <div className={`w-3 h-3 rounded-full ${
                  healthData.status === 'healthy' ? 'bg-green-500' : 'bg-red-500'
                }`}></div>
                <span className="font-semibold">
                  Status: {healthData.status.toUpperCase()}
                </span>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 border rounded-lg">
                <h3 className="font-semibold mb-2">System Info</h3>
                <p className="text-sm text-muted-foreground">
                  Version: {healthData.version}
                </p>
                <p className="text-sm text-muted-foreground">
                  Timestamp: {new Date(healthData.timestamp).toLocaleString()}
                </p>
              </div>

              {healthData.services && (
                <div className="p-4 border rounded-lg">
                  <h3 className="font-semibold mb-2">Services</h3>
                  <div className="space-y-1">
                    {Object.entries(healthData.services).map(([service, status]) => (
                      <div key={service} className="flex justify-between text-sm">
                        <span className="capitalize">{service}:</span>
                        <span className={
                          status === 'connected'
                            ? 'text-green-600'
                            : 'text-red-600'
                        }>
                          {String(status)}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
}
