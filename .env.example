# Environment Configuration for OmniCore
# Copy this file to .env and update the values as needed

# Application Environment
ENVIRONMENT=development
NODE_ENV=development

# Frontend Configuration
NEXT_PUBLIC_API_URL=http://localhost:8080
NEXT_PUBLIC_APP_NAME=OmniCore

# Backend Configuration
PORT=8080

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=postgres
DB_NAME=omnicore
DB_SSLMODE=disable

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Kafka Configuration
KAFKA_BROKERS=localhost:9092

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# Docker Compose Override (optional)
COMPOSE_PROJECT_NAME=omnicore
