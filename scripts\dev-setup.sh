#!/bin/bash

# OmniCore Development Setup Script
echo "🚀 Setting up OmniCore development environment..."

# Check if Dock<PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from .env.example..."
    cp .env.example .env
    echo "✅ .env file created. Please review and update the values as needed."
fi

# Install root dependencies
echo "📦 Installing root dependencies..."
npm install

# Install frontend dependencies
echo "📦 Installing frontend dependencies..."
cd apps/frontend && npm install && cd ../..

# Build and start services
echo "🐳 Starting Docker services..."
docker-compose up -d postgres redis zookeeper kafka

# Wait for services to be healthy
echo "⏳ Waiting for services to be ready..."
sleep 30

# Check service health
echo "🔍 Checking service health..."
docker-compose ps

echo "✅ Development environment setup complete!"
echo ""
echo "🎯 Next steps:"
echo "  1. Review .env file and update values as needed"
echo "  2. Run 'npm run dev' to start the development servers"
echo "  3. Visit http://localhost:3000 for frontend"
echo "  4. Visit http://localhost:8080/api/v1/health for backend health check"
echo ""
echo "🛠️  Useful commands:"
echo "  - npm run dev          # Start all development servers"
echo "  - npm run docker:dev   # Start Docker services only"
echo "  - npm run docker:down  # Stop Docker services"
echo "  - npm run build        # Build all applications"
echo "  - npm run test         # Run all tests"
