# OmniCore - Integrated Business Management Ecosystem

A comprehensive business management platform built with modern technologies, featuring Warehouse Management (WMS), Point of Sale (POS), and Accounting systems in a unified monorepo architecture.

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ and npm 9+
- Docker Desktop
- Go 1.22+ (for backend development)

### Setup

1. **Clone and install dependencies:**
   ```bash
   git clone <repository-url>
   cd OmniCore
   npm install
   ```

2. **Start development environment:**
   ```bash
   # Start all services (PostgreSQL, Redis, Kafka)
   npm run docker:dev
   
   # Start development servers
   npm run dev
   ```

3. **Access the applications:**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8080
   - Health Check: http://localhost:3000/health

## 🏗️ Architecture

### Tech Stack

- **Frontend**: Next.js 14+, TypeScript 5.4+, Tailwind CSS, Shadcn/ui, Zustand
- **Backend**: Go 1.22+, Gin web framework
- **Database**: PostgreSQL 16+
- **Cache**: Redis 7.2+
- **Message Queue**: Apache Kafka 3.7+
- **Containerization**: Docker
- **CI/CD**: GitHub Actions
- **Orchestration**: Kubernetes (production)

### Project Structure

```
OmniCore/
├── apps/
│   ├── frontend/          # Next.js application
│   └── backend/           # Go API server
├── packages/
│   └── types/             # Shared TypeScript types
├── infrastructure/
│   ├── k8s/              # Kubernetes manifests
│   └── postgres/         # Database initialization
├── scripts/              # Development scripts
└── docs/                 # Documentation
```

## 🛠️ Development

### Available Scripts

```bash
# Development
npm run dev              # Start all development servers
npm run build            # Build all applications
npm run test             # Run all tests
npm run lint             # Lint all code
npm run type-check       # TypeScript type checking

# Docker
npm run docker:dev       # Start Docker services
npm run docker:down      # Stop Docker services
npm run docker:logs      # View Docker logs
npm run docker:ps        # Check Docker status

# Setup
npm run setup            # Quick setup (install + docker)
npm run setup:full       # Full setup with scripts
npm run health           # Check backend health
```

### Frontend Development

```bash
cd apps/frontend
npm run dev              # Start development server
npm run test             # Run tests
npm run test:watch       # Run tests in watch mode
npm run build            # Build for production
```

### Backend Development

```bash
cd apps/backend
go run ./cmd/server      # Start development server
go test ./...            # Run tests
go build ./cmd/server    # Build binary
```

## 🧪 Testing

### Frontend Testing
- **Framework**: Jest + React Testing Library
- **Location**: Co-located with source files (`*.test.tsx`)
- **Coverage**: Run `npm run test:coverage` in frontend directory

### Backend Testing
- **Framework**: Go standard testing + Testify
- **Location**: Co-located with source files (`*_test.go`)
- **Coverage**: Run `go test -cover ./...` in backend directory

## 🚀 Deployment

### Development Environment
- **Auto-deployment**: On push to `main` branch
- **URL**: https://dev.omnicore.example.com
- **Platform**: Kubernetes

### Production Environment
- **Deployment**: Manual trigger with approval
- **URL**: https://omnicore.example.com
- **Platform**: Kubernetes (EKS)

See [docs/deployment.md](docs/deployment.md) for detailed deployment instructions.

## 📊 Monitoring

### Health Checks
- **Backend**: `GET /api/v1/health`
- **Frontend**: `/health` page
- **Services**: Database, Redis, Kafka connectivity

### Logs
```bash
# Application logs
kubectl logs -f deployment/backend-deployment -n omnicore-dev
kubectl logs -f deployment/frontend-deployment -n omnicore-dev

# Docker logs (development)
npm run docker:logs
```

## 🔒 Security

- Environment variables for sensitive configuration
- CORS protection
- JWT authentication (configured)
- Container security scanning in CI
- Dependency vulnerability scanning

## 🤝 Contributing

1. Create a feature branch from `develop`
2. Make your changes
3. Ensure all tests pass: `npm run test`
4. Ensure code quality: `npm run lint`
5. Create a pull request

### Code Standards
- TypeScript strict mode enabled
- ESLint + Prettier for frontend
- golangci-lint for backend
- 100% test coverage for critical paths

## 📝 Documentation

- [Deployment Guide](docs/deployment.md)
- [Architecture Documentation](docs/architecture.md)
- [API Documentation](docs/api.md)
- [Story Documentation](docs/stories/)

## 🆘 Troubleshooting

### Common Issues

1. **Docker services not starting**: Ensure Docker Desktop is running
2. **Port conflicts**: Check if ports 3000, 8080, 5432, 6379, 9092 are available
3. **Build failures**: Run `npm install` and ensure all dependencies are installed

### Getting Help

- Check the [troubleshooting guide](docs/deployment.md#troubleshooting)
- Review application logs
- Check service health endpoints

## 📄 License

[License information to be added]

---

Built with ❤️ by the OmniCore Team
