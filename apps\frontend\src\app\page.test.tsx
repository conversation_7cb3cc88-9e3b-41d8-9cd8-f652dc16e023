import { render, screen } from '@testing-library/react';
import Home from './page';

// Mock the Layout component
jest.mock('@/components/layout', () => ({
  Layout: ({ children }: { children: React.ReactNode }) => <div data-testid="layout">{children}</div>
}));

describe('Home Page', () => {
  it('renders welcome message', () => {
    render(<Home />);
    
    expect(screen.getByText('Welcome to OmniCore')).toBeInTheDocument();
    expect(screen.getByText('Integrated Business Management Ecosystem')).toBeInTheDocument();
  });

  it('renders all business modules', () => {
    render(<Home />);
    
    expect(screen.getByText('WMS')).toBeInTheDocument();
    expect(screen.getByText('POS')).toBeInTheDocument();
    expect(screen.getByText('Accounting')).toBeInTheDocument();
  });

  it('renders module descriptions', () => {
    render(<Home />);
    
    expect(screen.getByText('Warehouse Management System for inventory control')).toBeInTheDocument();
    expect(screen.getByText('Point of Sale system for retail operations')).toBeInTheDocument();
    expect(screen.getByText('Automated accounting and financial reporting')).toBeInTheDocument();
  });
});
