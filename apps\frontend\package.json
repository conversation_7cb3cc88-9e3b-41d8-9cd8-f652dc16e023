{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "type-check": "tsc --noEmit", "clean": "rm -rf .next out"}, "dependencies": {"autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.536.0", "next": "15.4.5", "postcss": "^8.5.6", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zustand": "^5.0.7", "@omnicore/types": "workspace:*"}, "devDependencies": {"@eslint/eslintrc": "^3", "@testing-library/jest-dom": "^6.1.0", "@testing-library/react": "^14.1.0", "@testing-library/user-event": "^14.5.0", "@types/jest": "^29.5.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.5", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "tailwindcss": "^3.4.17", "typescript": "^5"}}