name: CD Pipeline

on:
  push:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'development'
        type: choice
        options:
          - development
          - staging
          - production

env:
  REGISTRY: ghcr.io
  IMAGE_NAME_FRONTEND: ${{ github.repository }}/frontend
  IMAGE_NAME_BACKEND: ${{ github.repository }}/backend

jobs:
  # Build and push Docker images
  build-images:
    name: Build and Push Images
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    outputs:
      frontend-image: ${{ steps.meta-frontend.outputs.tags }}
      backend-image: ${{ steps.meta-backend.outputs.tags }}
      frontend-digest: ${{ steps.build-frontend.outputs.digest }}
      backend-digest: ${{ steps.build-backend.outputs.digest }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      # Frontend image
      - name: Extract metadata for frontend
        id: meta-frontend
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME_FRONTEND }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push frontend image
        id: build-frontend
        uses: docker/build-push-action@v5
        with:
          context: ./apps/frontend
          file: ./apps/frontend/Dockerfile
          push: true
          tags: ${{ steps.meta-frontend.outputs.tags }}
          labels: ${{ steps.meta-frontend.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      # Backend image
      - name: Extract metadata for backend
        id: meta-backend
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME_BACKEND }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push backend image
        id: build-backend
        uses: docker/build-push-action@v5
        with:
          context: ./apps/backend
          file: ./apps/backend/Dockerfile
          push: true
          tags: ${{ steps.meta-backend.outputs.tags }}
          labels: ${{ steps.meta-backend.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  # Deploy to development environment
  deploy-development:
    name: Deploy to Development
    runs-on: ubuntu-latest
    needs: build-images
    if: github.ref == 'refs/heads/main' || github.event.inputs.environment == 'development'
    environment:
      name: development
      url: https://dev.omnicore.example.com

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'latest'

      - name: Configure kubectl
        run: |
          echo "${{ secrets.KUBE_CONFIG_DEV }}" | base64 -d > kubeconfig
          export KUBECONFIG=kubeconfig

      - name: Deploy to Kubernetes
        run: |
          export KUBECONFIG=kubeconfig
          
          # Update image tags in deployment manifests
          sed -i "s|IMAGE_TAG_FRONTEND|${{ needs.build-images.outputs.frontend-image }}|g" infrastructure/k8s/development/frontend-deployment.yaml
          sed -i "s|IMAGE_TAG_BACKEND|${{ needs.build-images.outputs.backend-image }}|g" infrastructure/k8s/development/backend-deployment.yaml
          
          # Apply manifests
          kubectl apply -f infrastructure/k8s/development/
          
          # Wait for rollout
          kubectl rollout status deployment/frontend-deployment -n omnicore-dev --timeout=300s
          kubectl rollout status deployment/backend-deployment -n omnicore-dev --timeout=300s

      - name: Run deployment verification
        run: |
          export KUBECONFIG=kubeconfig
          
          # Get service URL
          BACKEND_URL=$(kubectl get service backend-service -n omnicore-dev -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
          
          # Wait for service to be ready
          sleep 30
          
          # Test health endpoint
          curl -f http://$BACKEND_URL:8080/api/v1/health || exit 1
          
          echo "✅ Deployment verification successful"

      - name: Notify deployment status
        if: always()
        run: |
          if [ "${{ job.status }}" == "success" ]; then
            echo "✅ Development deployment successful"
          else
            echo "❌ Development deployment failed"
            exit 1
          fi
